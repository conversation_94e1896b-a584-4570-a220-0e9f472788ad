# 定时器基类使用指南

## 概述

我已经为你的项目创建了一套完整的定时器解决方案，用于实现每1秒（或自定义间隔）向后端请求数据的功能。这套方案包含了多种使用方式，可以满足不同的业务需求。

## 文件结构

```
src/utils/
├── timer.ts                    # 基础定时器工具
├── DataPollingBase.ts          # 数据轮询基类
├── UserDataPolling.ts          # 用户数据轮询示例
└── README_Timer.md             # 详细API文档

src/components/
└── UserListWithPolling.vue     # 完整的用户列表轮询组件示例

src/examples/
├── SimplePollingExample.vue    # 简单使用示例
└── PortTypeWithPolling.vue     # 港口类型轮询示例
```

## 核心功能特性

✅ **自动轮询**: 支持定时自动请求数据  
✅ **灵活配置**: 可配置轮询间隔、是否立即执行等  
✅ **错误处理**: 内置错误处理机制  
✅ **类型安全**: 完整的 TypeScript 类型支持  
✅ **自动清理**: 组件卸载时自动清理定时器  
✅ **状态管理**: 提供加载状态、错误状态等响应式数据

## 快速开始

### 方式一：使用 Composable 函数（最简单）

```vue
<template>
  <div>
    <p>数据: {{ JSON.stringify(polling.data) }}</p>
    <p>状态: {{ polling.loading ? '加载中' : '完成' }}</p>
    <el-button @click="polling.start()">开始</el-button>
    <el-button @click="polling.stop()">停止</el-button>
  </div>
</template>

<script setup lang="ts">
import { useDataPolling } from '@/utils/timer'
import { getUserPage } from '@/api/system/user'

const polling = useDataPolling(
  async () => {
    return await getUserPage({ pageNo: 1, pageSize: 10 })
  },
  { interval: 1000 } // 每1秒请求一次
)
</script>
```

### 方式二：使用数据轮询基类（推荐）

```typescript
// 1. 创建自定义轮询类
import { DataPollingBase } from '@/utils/DataPollingBase'
import { getUserPage } from '@/api/system/user'

class MyUserPolling extends DataPollingBase<any, PageParam> {
  protected async fetchData(params: PageParam) {
    return await getUserPage(params)
  }

  protected onDataUpdated(result: PageResult<any>) {
    console.log('数据已更新:', result.total)
  }
}

// 2. 在组件中使用
const userPolling = new MyUserPolling({ pageNo: 1, pageSize: 10 }, { interval: 1000 })

// 3. 在模板中使用
// userPolling.data - 数据列表
// userPolling.total - 总数
// userPolling.loading - 加载状态
// userPolling.isRunning - 轮询状态
```

### 方式三：直接使用现成的用户轮询类

```vue
<script setup lang="ts">
import { UserDataPolling } from '@/utils/UserDataPolling'

const userPolling = new UserDataPolling({ pageNo: 1, pageSize: 10 }, { interval: 1000 })

// 控制方法
const togglePolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
  } else {
    userPolling.start()
  }
}
</script>
```

## 配置选项

```typescript
interface TimerConfig {
  interval?: number // 轮询间隔（毫秒），默认 1000
  immediate?: boolean // 是否立即执行一次，默认 true
  autoStart?: boolean // 是否自动开始，默认 true
}

interface DataPollingConfig extends TimerConfig {
  continueOnError?: boolean // 出错时是否继续轮询，默认 true
}
```

## 常用方法

```typescript
// 控制轮询
polling.start() // 开始轮询
polling.stop() // 停止轮询
polling.restart() // 重启轮询
polling.refresh() // 手动刷新一次

// 更新参数
polling.updateParams({ pageNo: 2 }) // 更新查询参数
polling.setInterval(2000) // 更改轮询间隔

// 状态检查
polling.isRunning // 是否正在轮询
polling.loading.value // 是否正在加载
polling.error.value // 错误信息
```

## 实际应用示例

### 用户管理页面实时更新

参考 `src/components/UserListWithPolling.vue`，这是一个完整的用户列表页面，包含：

- 实时数据轮询
- 轮询控制面板
- 分页支持
- 错误处理
- 状态显示

### 港口类型页面实时更新

参考 `src/examples/PortTypeWithPolling.vue`，展示了如何在现有业务页面中集成轮询功能。

## 最佳实践

1. **选择合适的方式**：

   - 简单场景用 Composable 函数
   - 复杂业务逻辑用基类继承
   - 用户数据直接用 UserDataPolling

2. **合理设置间隔**：

   - 实时性要求高：500-1000ms
   - 一般业务场景：2000-5000ms
   - 后台监控：10000ms以上

3. **错误处理**：

   - 重要数据设置 `continueOnError: false`
   - 添加用户友好的错误提示
   - 考虑网络异常时的重试机制

4. **性能优化**：
   - 页面不可见时停止轮询
   - 避免同时运行多个高频轮询
   - 合理使用缓存

## 注意事项

⚠️ **内存泄漏防护**: 所有定时器都会在组件卸载时自动清理  
⚠️ **网络请求频率**: 请根据服务器承载能力合理设置轮询间隔  
⚠️ **用户体验**: 提供明确的轮询状态指示和控制按钮  
⚠️ **错误处理**: 确保网络错误不会影响用户正常操作

## 扩展开发

如果需要为其他API创建轮询类，可以参考 `UserDataPolling.ts` 的实现方式：

```typescript
import { DataPollingBase } from '@/utils/DataPollingBase'
import { getYourApiPage } from '@/api/your-module'

export class YourDataPolling extends DataPollingBase<YourDataType, YourParamType> {
  protected async fetchData(params: YourParamType) {
    return await getYourApiPage(params)
  }

  protected onDataUpdated(result: PageResult<YourDataType>) {
    // 自定义数据更新逻辑
  }

  protected onError(error: Error) {
    // 自定义错误处理逻辑
  }
}
```

## 问题排查

如果遇到 `data.includes is not a function` 错误，请按以下步骤排查：

### 1. 使用调试版本

```typescript
import { DebugUserDataPolling } from '@/utils/DebugUserDataPolling'

const userPolling = new DebugUserDataPolling({ pageNo: 1, pageSize: 10 }, { interval: 3000 })
```

### 2. 检查API返回数据格式

确保你的API返回格式如下：

```typescript
{
  list: Array,    // 必须是数组
  total: number   // 必须是数字
}
```

### 3. 常见问题解决

- **数据格式错误**: 检查API返回的 `list` 字段是否为数组
- **类型错误**: 确保 `total` 字段是数字类型
- **网络错误**: 检查网络连接和API地址是否正确

### 4. 测试组件

使用 `src/components/DebugPollingTest.vue` 进行测试：

```vue
<template>
  <DebugPollingTest />
</template>

<script setup>
import DebugPollingTest from '@/components/DebugPollingTest.vue'
</script>
```

现在你可以在任何需要实时数据更新的组件中使用这些工具了！
