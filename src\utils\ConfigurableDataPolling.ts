import { DataPollingBase, type DataPollingConfig } from './DataPollingBase'

/**
 * API函数类型定义
 */
export type ApiFunction<TData = any, TParams = any> = (params: TParams) => Promise<PageResult<TData>>

/**
 * 可配置API的数据轮询类
 * 允许在运行时动态配置API函数
 */
export class ConfigurableDataPolling<TData = any, TParams = PageParam> extends DataPollingBase<TData, TParams> {
  private apiFunction: ApiFunction<TData, TParams>
  private onUpdateCallback?: (result: PageResult<TData>) => void
  private onErrorCallback?: (error: Error) => void

  constructor(
    apiFunction: ApiFunction<TData, TParams>,
    initialParams: Partial<TParams> = {},
    config: DataPollingConfig = {},
    callbacks?: {
      onUpdate?: (result: PageResult<TData>) => void
      onError?: (error: Error) => void
    }
  ) {
    super(initialParams, config)
    
    this.apiFunction = apiFunction
    this.onUpdateCallback = callbacks?.onUpdate
    this.onErrorCallback = callbacks?.onError
    
    // 自动初始化轮询
    this.initPolling()
  }

  /**
   * 实现抽象方法：调用配置的API函数
   */
  protected async fetchData(params: TParams): Promise<PageResult<TData>> {
    return await this.apiFunction(params)
  }

  /**
   * 重写数据更新回调
   */
  protected onDataUpdated(result: PageResult<TData>): void {
    this.onUpdateCallback?.(result)
  }

  /**
   * 重写错误处理回调
   */
  protected onError(error: Error): void {
    this.onErrorCallback?.(error)
    console.error('ConfigurableDataPolling error:', error.message)
  }

  /**
   * 动态更换API函数
   */
  public setApiFunction(
    newApiFunction: ApiFunction<TData, TParams>,
    callbacks?: {
      onUpdate?: (result: PageResult<TData>) => void
      onError?: (error: Error) => void
    }
  ): void {
    this.apiFunction = newApiFunction
    
    if (callbacks?.onUpdate) {
      this.onUpdateCallback = callbacks.onUpdate
    }
    
    if (callbacks?.onError) {
      this.onErrorCallback = callbacks.onError
    }
    
    // 如果正在运行，重启轮询以使用新的API
    if (this.isRunning) {
      this.restart()
    }
  }

  /**
   * 设置回调函数
   */
  public setCallbacks(callbacks: {
    onUpdate?: (result: PageResult<TData>) => void
    onError?: (error: Error) => void
  }): void {
    if (callbacks.onUpdate) {
      this.onUpdateCallback = callbacks.onUpdate
    }
    
    if (callbacks.onError) {
      this.onErrorCallback = callbacks.onError
    }
  }
}

/**
 * 创建可配置轮询的工厂函数
 */
export function createConfigurablePolling<TData = any, TParams = PageParam>(
  apiFunction: ApiFunction<TData, TParams>,
  initialParams: Partial<TParams> = {},
  config: DataPollingConfig = {}
) {
  return new ConfigurableDataPolling<TData, TParams>(apiFunction, initialParams, config)
}

/**
 * 轮询管理器 - 管理多个轮询实例
 */
export class PollingManager {
  private pollings: Map<string, DataPollingBase<any, any>> = new Map()

  /**
   * 添加轮询实例
   */
  public add(name: string, polling: DataPollingBase<any, any>): void {
    // 如果已存在同名轮询，先销毁
    if (this.pollings.has(name)) {
      this.pollings.get(name)?.destroy()
    }
    
    this.pollings.set(name, polling)
  }

  /**
   * 获取轮询实例
   */
  public get(name: string): DataPollingBase<any, any> | undefined {
    return this.pollings.get(name)
  }

  /**
   * 移除轮询实例
   */
  public remove(name: string): boolean {
    const polling = this.pollings.get(name)
    if (polling) {
      polling.destroy()
      return this.pollings.delete(name)
    }
    return false
  }

  /**
   * 启动所有轮询
   */
  public startAll(): void {
    this.pollings.forEach(polling => polling.start())
  }

  /**
   * 停止所有轮询
   */
  public stopAll(): void {
    this.pollings.forEach(polling => polling.stop())
  }

  /**
   * 刷新所有轮询数据
   */
  public async refreshAll(): Promise<void> {
    const promises = Array.from(this.pollings.values()).map(polling => polling.refresh())
    await Promise.all(promises)
  }

  /**
   * 获取所有轮询的状态
   */
  public getStatus(): Record<string, { isRunning: boolean; loading: boolean; error: string | null }> {
    const status: Record<string, { isRunning: boolean; loading: boolean; error: string | null }> = {}
    
    this.pollings.forEach((polling, name) => {
      status[name] = {
        isRunning: polling.isRunning,
        loading: polling.loading.value,
        error: polling.error.value?.message || null
      }
    })
    
    return status
  }

  /**
   * 销毁所有轮询
   */
  public destroyAll(): void {
    this.pollings.forEach(polling => polling.destroy())
    this.pollings.clear()
  }

  /**
   * 获取轮询数量
   */
  public get size(): number {
    return this.pollings.size
  }

  /**
   * 获取所有轮询名称
   */
  public getNames(): string[] {
    return Array.from(this.pollings.keys())
  }
}
