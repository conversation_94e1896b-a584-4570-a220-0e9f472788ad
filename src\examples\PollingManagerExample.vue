<template>
  <div class="polling-manager-example">
    <h2>轮询管理器示例</h2>
    
    <!-- 轮询管理控制面板 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>轮询管理控制面板</span>
      </template>
      
      <div class="manager-controls">
        <el-button @click="addUserPolling">添加用户轮询</el-button>
        <el-button @click="addPortTypePolling">添加港口类型轮询</el-button>
        <el-button @click="addCustomPolling">添加自定义轮询</el-button>
        <el-divider direction="vertical" />
        <el-button type="success" @click="manager.startAll()">启动全部</el-button>
        <el-button type="warning" @click="manager.stopAll()">停止全部</el-button>
        <el-button type="info" @click="manager.refreshAll()">刷新全部</el-button>
        <el-button type="danger" @click="clearAllPolling">清空全部</el-button>
      </div>
      
      <div class="manager-status" style="margin-top: 15px;">
        <p><strong>轮询状态总览:</strong></p>
        <el-table :data="statusTableData" size="small" border>
          <el-table-column prop="name" label="轮询名称" width="150" />
          <el-table-column prop="isRunning" label="运行状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.isRunning ? 'success' : 'info'">
                {{ row.isRunning ? '运行中' : '已停止' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="loading" label="加载状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.loading ? 'warning' : 'success'">
                {{ row.loading ? '加载中' : '空闲' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="error" label="错误信息">
            <template #default="{ row }">
              <span :style="{ color: row.error ? 'red' : 'green' }">
                {{ row.error || '无错误' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button 
                size="small" 
                :type="row.isRunning ? 'danger' : 'primary'"
                @click="togglePolling(row.name)"
              >
                {{ row.isRunning ? '停止' : '启动' }}
              </el-button>
              <el-button size="small" @click="refreshPolling(row.name)">刷新</el-button>
              <el-button size="small" type="danger" @click="removePolling(row.name)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 动态API配置 -->
    <el-card>
      <template #header>
        <span>动态API配置示例</span>
      </template>
      
      <div class="api-config">
        <el-form :model="apiConfig" label-width="120px">
          <el-form-item label="API类型:">
            <el-select v-model="apiConfig.type" @change="switchApi">
              <el-option label="用户API" value="user" />
              <el-option label="港口类型API" value="portType" />
              <el-option label="模拟API" value="mock" />
            </el-select>
          </el-form-item>
          <el-form-item label="轮询间隔:">
            <el-input-number 
              v-model="apiConfig.interval" 
              :min="500" 
              :max="10000" 
              :step="500"
              @change="updateInterval"
            />
            <span style="margin-left: 10px;">毫秒</span>
          </el-form-item>
        </el-form>
        
        <div v-if="configurablePolling" class="configurable-status">
          <h4>可配置轮询状态:</h4>
          <p>运行状态: {{ configurablePolling.isRunning ? '运行中' : '已停止' }}</p>
          <p>数据条数: {{ configurablePolling.data.value.length }}</p>
          <p>总数: {{ configurablePolling.total.value }}</p>
          <p>加载状态: {{ configurablePolling.loading.value ? '加载中' : '空闲' }}</p>
          <p>错误信息: {{ configurablePolling.error.value?.message || '无' }}</p>
          
          <div style="margin-top: 10px;">
            <el-button 
              :type="configurablePolling.isRunning ? 'danger' : 'primary'"
              @click="toggleConfigurablePolling"
            >
              {{ configurablePolling.isRunning ? '停止' : '启动' }}
            </el-button>
            <el-button @click="configurablePolling.refresh()">刷新</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { PollingManager, ConfigurableDataPolling, createConfigurablePolling } from '@/utils/ConfigurableDataPolling'
import { UserDataPolling } from '@/utils/UserDataPolling'
import { getUserPage } from '@/api/system/user'

/** 轮询管理器示例组件 */
defineOptions({ name: 'PollingManagerExample' })

// 创建轮询管理器实例
const manager = new PollingManager()

// API配置
const apiConfig = ref({
  type: 'user',
  interval: 2000
})

// 可配置轮询实例
let configurablePolling: ConfigurableDataPolling | null = null

// 状态表格数据
const statusTableData = computed(() => {
  const status = manager.getStatus()
  return Object.entries(status).map(([name, data]) => ({
    name,
    ...data
  }))
})

// ========== 轮询管理方法 ==========

/**
 * 添加用户轮询
 */
const addUserPolling = () => {
  const userPolling = new UserDataPolling(
    { pageNo: 1, pageSize: 5 },
    { interval: 3000, autoStart: true }
  )
  manager.add('用户数据', userPolling)
}

/**
 * 添加港口类型轮询
 */
const addPortTypePolling = () => {
  const portTypePolling = createConfigurablePolling(
    async (params) => {
      // 模拟港口类型API
      await new Promise(resolve => setTimeout(resolve, 500))
      return {
        list: [
          { id: 1, name: '集装箱港口', code: 'CONTAINER' },
          { id: 2, name: '散货港口', code: 'BULK' }
        ],
        total: 2
      }
    },
    { pageNo: 1, pageSize: 10 },
    { interval: 4000, autoStart: true }
  )
  manager.add('港口类型', portTypePolling)
}

/**
 * 添加自定义轮询
 */
const addCustomPolling = () => {
  const customPolling = createConfigurablePolling(
    async (params) => {
      // 模拟自定义API
      await new Promise(resolve => setTimeout(resolve, 300))
      return {
        list: [
          { id: 1, timestamp: new Date().toISOString(), random: Math.random() }
        ],
        total: 1
      }
    },
    { pageNo: 1, pageSize: 20 },
    { interval: 5000, autoStart: true }
  )
  manager.add('自定义数据', customPolling)
}

/**
 * 切换指定轮询的状态
 */
const togglePolling = (name: string) => {
  const polling = manager.get(name)
  if (polling) {
    if (polling.isRunning) {
      polling.stop()
    } else {
      polling.start()
    }
  }
}

/**
 * 刷新指定轮询
 */
const refreshPolling = (name: string) => {
  const polling = manager.get(name)
  if (polling) {
    polling.refresh()
  }
}

/**
 * 移除指定轮询
 */
const removePolling = (name: string) => {
  manager.remove(name)
}

/**
 * 清空所有轮询
 */
const clearAllPolling = () => {
  manager.destroyAll()
}

// ========== 可配置API方法 ==========

/**
 * 初始化可配置轮询
 */
const initConfigurablePolling = () => {
  configurablePolling = createConfigurablePolling(
    getUserPage, // 默认使用用户API
    { pageNo: 1, pageSize: 5 },
    { 
      interval: apiConfig.value.interval, 
      autoStart: true 
    }
  )
}

/**
 * 切换API
 */
const switchApi = () => {
  if (!configurablePolling) return

  let newApiFunction
  
  switch (apiConfig.value.type) {
    case 'user':
      newApiFunction = getUserPage
      break
    case 'portType':
      newApiFunction = async (params: any) => {
        await new Promise(resolve => setTimeout(resolve, 500))
        return {
          list: [
            { id: 1, name: '集装箱港口', code: 'CONTAINER' },
            { id: 2, name: '散货港口', code: 'BULK' }
          ],
          total: 2
        }
      }
      break
    case 'mock':
      newApiFunction = async (params: any) => {
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          list: [
            { id: 1, data: `模拟数据 ${new Date().toLocaleTimeString()}`, random: Math.random() }
          ],
          total: 1
        }
      }
      break
    default:
      return
  }

  configurablePolling.setApiFunction(newApiFunction, {
    onUpdate: (result) => console.log(`${apiConfig.value.type} API数据已更新:`, result.total),
    onError: (error) => console.error(`${apiConfig.value.type} API错误:`, error.message)
  })
}

/**
 * 更新轮询间隔
 */
const updateInterval = () => {
  if (configurablePolling) {
    configurablePolling.setInterval(apiConfig.value.interval)
  }
}

/**
 * 切换可配置轮询状态
 */
const toggleConfigurablePolling = () => {
  if (!configurablePolling) return
  
  if (configurablePolling.isRunning) {
    configurablePolling.stop()
  } else {
    configurablePolling.start()
  }
}

// 初始化
initConfigurablePolling()

// 组件卸载时清理
onUnmounted(() => {
  manager.destroyAll()
  configurablePolling?.destroy()
})
</script>

<style scoped>
.polling-manager-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.manager-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.manager-status {
  margin-top: 15px;
}

.api-config {
  max-width: 600px;
}

.configurable-status {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.configurable-status h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.configurable-status p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
