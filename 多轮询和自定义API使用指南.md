# 多轮询和自定义API使用指南

## 概述

本指南介绍如何在一个组件中开启多个轮询，以及如何配置自定义API进行轮询。

## 一、在组件中开启多个轮询

### 方式一：创建多个轮询实例

```vue
<script setup lang="ts">
import { UserDataPolling } from '@/utils/UserDataPolling'
import { DataPollingBase } from '@/utils/DataPollingBase'

// 创建多个不同的轮询实例
const userPolling = new UserDataPolling(
  { pageNo: 1, pageSize: 10 },
  { interval: 2000, autoStart: true }
)

const orderPolling = new OrderDataPolling(
  { status: 'pending' },
  { interval: 5000, autoStart: true }
)

const statusPolling = new SystemStatusPolling(
  {},
  { interval: 10000, autoStart: true }
)

// 组件卸载时清理所有轮询
onUnmounted(() => {
  userPolling.destroy()
  orderPolling.destroy()
  statusPolling.destroy()
})
</script>
```

### 方式二：使用轮询管理器

```vue
<script setup lang="ts">
import { PollingManager } from '@/utils/ConfigurableDataPolling'

// 创建轮询管理器
const manager = new PollingManager()

// 添加多个轮询
manager.add('用户数据', new UserDataPolling({ pageNo: 1, pageSize: 10 }))
manager.add('订单数据', new OrderDataPolling({ status: 'pending' }))
manager.add('系统状态', new SystemStatusPolling({}))

// 统一控制
const startAll = () => manager.startAll()
const stopAll = () => manager.stopAll()
const refreshAll = () => manager.refreshAll()

// 组件卸载时清理
onUnmounted(() => {
  manager.destroyAll()
})
</script>
```

## 二、配置自定义API

### 方式一：继承DataPollingBase

```typescript
import { DataPollingBase } from '@/utils/DataPollingBase'
import { getYourCustomApi } from '@/api/your-module'

class CustomDataPolling extends DataPollingBase<YourDataType, YourParamType> {
  protected async fetchData(params: YourParamType): Promise<PageResult<YourDataType>> {
    // 调用你的自定义API
    return await getYourCustomApi(params)
  }

  protected onDataUpdated(result: PageResult<YourDataType>): void {
    console.log('自定义数据已更新:', result.total)
    // 添加你的自定义逻辑
  }

  protected onError(error: Error): void {
    console.error('自定义API错误:', error.message)
    // 添加你的错误处理逻辑
  }
}

// 使用
const customPolling = new CustomDataPolling(
  { pageNo: 1, pageSize: 20 },
  { interval: 3000, autoStart: true }
)
```

### 方式二：使用可配置轮询类

```typescript
import { createConfigurablePolling } from '@/utils/ConfigurableDataPolling'
import { getYourCustomApi } from '@/api/your-module'

// 创建可配置轮询
const polling = createConfigurablePolling(
  getYourCustomApi, // 你的API函数
  { pageNo: 1, pageSize: 20 }, // 初始参数
  { interval: 3000, autoStart: true } // 轮询配置
)

// 动态切换API
polling.setApiFunction(getAnotherApi, {
  onUpdate: (result) => console.log('数据更新:', result),
  onError: (error) => console.error('API错误:', error)
})
```

### 方式三：直接使用Composable

```typescript
import { useDataPolling } from '@/utils/timer'
import { getYourCustomApi } from '@/api/your-module'

const polling = useDataPolling(
  async () => {
    return await getYourCustomApi({ pageNo: 1, pageSize: 10 })
  },
  { interval: 2000, autoStart: true }
)
```

## 三、API函数要求

### 标准API函数格式

```typescript
// API函数必须返回PageResult格式
async function yourCustomApi(params: YourParamType): Promise<PageResult<YourDataType>> {
  const response = await fetch('/your-api-endpoint', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params)
  })
  
  const data = await response.json()
  
  // 确保返回标准格式
  return {
    list: data.items || [], // 数据列表
    total: data.total || 0   // 总数
  }
}
```

### 适配现有API

如果你的API返回格式不是标准的PageResult，可以创建适配器：

```typescript
import { getYourExistingApi } from '@/api/existing'

// 创建适配器函数
async function adaptedApi(params: any): Promise<PageResult<any>> {
  const response = await getYourExistingApi(params)
  
  // 适配返回格式
  return {
    list: response.data || response.items || [],
    total: response.count || response.totalCount || 0
  }
}

// 使用适配器
const polling = createConfigurablePolling(adaptedApi, params, config)
```

## 四、实际应用示例

### 示例1：电商后台多数据轮询

```vue
<script setup lang="ts">
import { PollingManager } from '@/utils/ConfigurableDataPolling'
import { 
  getOrderPage, 
  getUserPage, 
  getProductPage,
  getSystemStatus 
} from '@/api'

const manager = new PollingManager()

// 添加订单轮询（每5秒）
manager.add('orders', createConfigurablePolling(
  getOrderPage,
  { status: 'pending', pageNo: 1, pageSize: 10 },
  { interval: 5000, autoStart: true }
))

// 添加用户轮询（每10秒）
manager.add('users', createConfigurablePolling(
  getUserPage,
  { pageNo: 1, pageSize: 20 },
  { interval: 10000, autoStart: true }
))

// 添加商品轮询（每30秒）
manager.add('products', createConfigurablePolling(
  getProductPage,
  { category: 'hot', pageNo: 1, pageSize: 50 },
  { interval: 30000, autoStart: true }
))

// 添加系统状态轮询（每60秒）
manager.add('system', createConfigurablePolling(
  getSystemStatus,
  {},
  { interval: 60000, autoStart: true }
))
</script>
```

### 示例2：监控大屏多指标轮询

```vue
<script setup lang="ts">
// 不同指标使用不同的轮询频率
const cpuPolling = createConfigurablePolling(getCpuMetrics, {}, { interval: 1000 })
const memoryPolling = createConfigurablePolling(getMemoryMetrics, {}, { interval: 2000 })
const diskPolling = createConfigurablePolling(getDiskMetrics, {}, { interval: 5000 })
const networkPolling = createConfigurablePolling(getNetworkMetrics, {}, { interval: 3000 })

// 根据告警级别动态调整轮询频率
const adjustPollingFrequency = (alertLevel: string) => {
  const interval = alertLevel === 'critical' ? 500 : 
                   alertLevel === 'warning' ? 1000 : 2000
  
  cpuPolling.setInterval(interval)
  memoryPolling.setInterval(interval)
}
</script>
```

## 五、最佳实践

### 1. 轮询频率设置

```typescript
// 根据数据重要性设置不同频率
const criticalData = { interval: 1000 }  // 关键数据：1秒
const importantData = { interval: 5000 } // 重要数据：5秒
const normalData = { interval: 30000 }   // 普通数据：30秒
const backgroundData = { interval: 60000 } // 后台数据：1分钟
```

### 2. 错误处理策略

```typescript
const polling = createConfigurablePolling(
  yourApi,
  params,
  { 
    interval: 5000,
    continueOnError: true // 出错时继续轮询
  }
)

polling.setCallbacks({
  onError: (error) => {
    // 根据错误类型采取不同策略
    if (error.message.includes('网络')) {
      // 网络错误：降低轮询频率
      polling.setInterval(10000)
    } else if (error.message.includes('权限')) {
      // 权限错误：停止轮询
      polling.stop()
    }
  }
})
```

### 3. 内存管理

```typescript
// 组件卸载时必须清理
onUnmounted(() => {
  manager.destroyAll()
  // 或者单独清理
  polling1.destroy()
  polling2.destroy()
})

// 页面隐藏时暂停轮询
onMounted(() => {
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      manager.stopAll()
    } else {
      manager.startAll()
    }
  })
})
```

## 六、注意事项

⚠️ **性能考虑**: 避免同时运行过多高频轮询  
⚠️ **网络负载**: 合理设置轮询间隔，避免给服务器造成压力  
⚠️ **内存泄漏**: 组件卸载时必须销毁所有轮询实例  
⚠️ **用户体验**: 提供轮询状态指示和手动控制选项  
⚠️ **错误恢复**: 实现合适的错误处理和重试机制

通过以上方式，你可以灵活地在组件中配置多个轮询，并根据需要调用不同的API。
