<script setup lang="ts">
import { ref } from 'vue'
import { Delete, Edit } from '@element-plus/icons-vue'

const selectData = ref('')

const tableDataList = ref([
  {
    index: 1,
    time: '2024-01-15 10:30:00',
    code: 'CTRL001',
    type: '网络故障',
    content: '网络连接超时'
  },
  {
    index: 2,
    time: '2024-01-15 11:45:00',
    code: 'CTRL002',
    type: '硬件故障',
    content: '传感器异常'
  },
  {
    index: 3,
    time: '2024-01-15 14:20:00',
    code: 'CTRL003',
    type: '软件故障',
    content: '程序运行错误'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(100)

const titleData = [
  {
    label: '事件时间',
    prop: 'time'
  },
  {
    label: '控制器代码',
    prop: 'code'
  },
  {
    label: '故障类型',
    prop: 'type'
  },
  {
    label: '故障内容',
    prop: 'content'
  }
]

// 分页方法
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  // 这里可以调用查询方法
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  // 这里可以调用查询方法
}
</script>

<template>
  <div class="error-container">
    <el-form>
      <el-row :gutter="20" justify="start">
        <el-col :span="6">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="selectData"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="用户卡ID">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="JT代码">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="卡类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="消耗类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div style="display: flex; justify-content: end">
      <el-button class="data-button1">高级配置</el-button>
      <el-button class="data-button2">查询</el-button>
    </div>

    <div class="error-content">
      <el-table
      
        :data="tableDataList"
        height="600px"
        row-class-name="rowStyle"
        header-row-class-name="headerRowStyle"
        header-cell-class-name="headerCellStyle"
        cell-class-name="cellStyle"
      >
        <el-table-column prop="index" label="序号" />
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          v-for="(item, index) in titleData"
          :key="index"
        />
      </el-table>
    </div>

    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.error-container {
  margin: 20px;
}

.data-button1 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: rgba(66, 180, 244, 1);
}

.data-button2 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #00bfb3 0%, #0076a9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: #409eff;
}

.error-content {
  margin-top: 50px;
}

.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

//这里进行修改table表格的样式内容：
// 使用深度选择器穿透 scoped 样式
:deep(.headerRowStyle) {
  background: rgba(23, 40, 96, 1);
  border: none;
}

:deep(.headerCellStyle) {
  color: rgba(22, 157, 255, 1);
  font-family: 'Microsoft YaHei', sans-serif;
  font-weight: 700;
  font-size: 16px; // 30px 太大了，建议改小
  line-height: 1.2;
  letter-spacing: 0px;
  text-align: center;
}

:deep(.rowStyle) {
  background: rgba(0, 26, 44, 0.7);
}

:deep(.rowStyle:hover) {
  background: rgba(23, 40, 96, 1);
}

:deep(.cellStyle) {
  text-align: center;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-style: Regular;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 0px;
}
</style>
