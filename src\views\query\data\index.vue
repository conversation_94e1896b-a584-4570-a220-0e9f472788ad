<script setup lang="ts">
import { ref } from 'vue'
import { Delete, Edit } from '@element-plus/icons-vue'

const selectData = ref('')

const tableDataList = ref([])

const titleData = [
  {
    label: '事件时间',
    prop: 'time'
  },
  {
    label: '控制器代码',
    prop: 'code'
  },
  {
    label: '故障类型',
    prop: 'type'
  },
  {
    label: '故障内容',
    prop: 'content'
  }
]
</script>

<template>
  <div class="error-container">
    <el-form>
      <el-row :gutter="20" justify="start">
        <el-col :span="6">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="selectData"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="用户卡ID">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="JT代码">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="卡类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="消耗类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div style="display: flex; justify-content: end">
      <el-button class="data-button1">高级配置</el-button>
      <el-button class="data-button2">查询</el-button>
    </div>

    <div class="error-content">
      <el-table
        :data="tableDataList"
        class="table"
        height="600px"
        header-row-class-name="rowStyle"
        header-cell-class-name="cellStyle"
        cell-class-name="cellStyle"
      >
        <el-table-column prop="index" label="序号" />
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          v-for="(item, index) in titleData"
          :key="index"
        />
      </el-table>
    </div>

    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.error-container {
  margin: 20px;
}

.data-button1 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: rgba(66, 180, 244, 1);
}

.data-button2 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #00bfb3 0%, #0076a9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: #409eff;
}

.error-content {
  margin-top: 50px;
}

.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
